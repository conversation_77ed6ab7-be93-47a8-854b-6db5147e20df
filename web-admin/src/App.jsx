import React, { useState, useEffect } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css'; // 引入 Bootstrap CSS

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loginError, setLoginError] = useState(null);

  const [pendingVideos, setPendingVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 检查本地存储中是否有 token
    const token = localStorage.getItem('adminToken');
    if (token) {
      setIsLoggedIn(true);
    }
  }, []);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoginError(null);
    try {
      const response = await fetch('http://localhost:3000/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      localStorage.setItem('adminToken', data.token);
      setIsLoggedIn(true);
    } catch (err) {
      setLoginError(err.message);
      console.error('登录失败:', err);
    }
  };

  const fetchPendingVideos = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) {
        setIsLoggedIn(false); // 没有 token，强制回到登录页
        return;
      }

      const response = await fetch('http://localhost:3000/api/admin/videos', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.status === 401 || response.status === 403) {
        localStorage.removeItem('adminToken'); // token 无效，清除并重新登录
        setIsLoggedIn(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setPendingVideos(data);
    } catch (err) {
      setError('获取视频列表失败: ' + err.message);
      console.error('获取视频列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isLoggedIn) {
      fetchPendingVideos();
    }
  }, [isLoggedIn]);

  const handleReview = async (id, action) => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) {
        setIsLoggedIn(false); // 没有 token，强制回到登录页
        return;
      }

      const response = await fetch(`http://localhost:3000/api/videos/${id}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.status === 401 || response.status === 403) {
        localStorage.removeItem('adminToken');
        setIsLoggedIn(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      // 审核成功后，从列表中移除该视频
      setPendingVideos(pendingVideos.filter(video => video.id !== id));
      alert(`视频已${action === 'approve' ? '批准' : '拒绝'}！`);
    } catch (err) {
      alert(`操作失败: ${err.message}`);
      console.error('审核操作失败:', err);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    setIsLoggedIn(false);
    setUsername('');
    setPassword('');
  };

  if (!isLoggedIn) {
    return (
      <div className="login-container">
        <div className="login-card">
          <h2 className="login-title">管理员登录</h2>
          <form onSubmit={handleLogin}>
            <div className="login-form-group">
              <label htmlFor="usernameInput" className="login-label">用户名</label>
              <input
                type="text"
                className="login-input"
                id="usernameInput"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                placeholder="请输入用户名"
              />
            </div>
            <div className="login-form-group">
              <label htmlFor="passwordInput" className="login-label">密码</label>
              <input
                type="password"
                className="login-input"
                id="passwordInput"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                placeholder="请输入密码"
              />
            </div>
            {loginError && <div className="login-error">{loginError}</div>}
            <button type="submit" className="login-btn">登录</button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-5" style={{ backgroundColor: '#f0f2f5', minHeight: '100vh' }}>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="text-secondary fw-bold display-5">视频审核后台</h1>
        <button className="btn btn-outline-secondary rounded-2" onClick={handleLogout}>退出登录</button>
      </div>
      <p className="mb-5 text-center text-muted fs-5">当前有 <span className="badge bg-info text-dark fs-6 rounded-2 px-3 py-2">{pendingVideos.length}</span> 个视频待审核</p>

      {loading && (
        <div className="text-center mt-5">
          <div className="spinner-border text-secondary" role="status" style={{ width: '3rem', height: '3rem' }}>
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3 fs-5 text-secondary">正在加载待审核视频...</p>
        </div>
      )}

      {error && (
        <div className="alert alert-danger mt-5 text-center py-2 rounded-2" role="alert">
          <strong>错误!</strong> {error}
        </div>
      )}

      {!loading && !error && pendingVideos.length === 0 && (
        <div className="text-center mt-5">
          <p className="lead fs-4 text-success fw-bold">太棒了！所有视频都已审核完毕。</p>
          <p className="text-muted fs-5">暂时没有新的视频需要审核。</p>
        </div>
      )}

      <div className="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {pendingVideos.map(video => (
          <div key={video.id} className="col">
            <div className="card h-100 shadow-sm border-0 rounded-3" style={{ background: '#ffffff' }}>
              <div className="card-body d-flex flex-column">
                <h5 className="card-title fw-bold text-truncate mb-2 fs-4">标题: {video.title || '无'}</h5>
                <p className="card-text text-muted mb-3 fs-6">用户ID: {video.user_id}</p>
                <div className="ratio ratio-16x9 mb-3 rounded-2 overflow-hidden shadow-sm">
                  <video controls className="w-100 h-100 object-fit-cover" src={video.url}></video>
                </div>
                <div className="d-flex justify-content-between mt-auto">
                  <button
                    className="btn btn-danger btn-lg flex-fill me-2 rounded-2 shadow-sm"
                    onClick={() => handleReview(video.id, 'reject')}
                  >
                    拒绝
                  </button>
                  <button
                    className="btn btn-success btn-lg flex-fill ms-2 rounded-2 shadow-sm"
                    onClick={() => handleReview(video.id, 'approve')}
                  >
                    批准
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default App;
