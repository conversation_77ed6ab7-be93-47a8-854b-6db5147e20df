const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const knex = require('knex');
const cors = require('cors');

const app = express();
const port = 3000;

// --- 数据库配置 (Knex + SQLite) ---
const db = knex({
  client: 'sqlite3',
  connection: {
    filename: path.join(__dirname, 'db.sqlite3'),
  },
  useNullAsDefault: true,
});

// 启动时检查并创建数据表
async function setupDatabase() {
  const tableExists = await db.schema.hasTable('videos');
  if (!tableExists) {
    console.log('Creating videos table...');
    await db.schema.createTable('videos', (table) => {
      table.increments('id').primary();
      table.string('filename').notNullable();
      table.string('url').notNullable();
      table.string('user_id').notNullable();
      table.string('title'); // 新增 title 字段
      table.string('status').defaultTo('pending'); // pending, approved, rejected
      table.timestamps(true, true);
    });
    console.log('Videos table created.');
  } else {
    console.log('Videos table already exists.');
  }
}

// --- Multer 文件上传配置 ---
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExtension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + fileExtension);
  },
});

const upload = multer({ storage: storage });

// --- 中间件 ---
app.use(cors()); // 启用 CORS
app.use(express.json()); // 用于解析 POST 请求的 JSON body
app.use('/uploads', express.static(path.join(__dirname, 'uploads'))); // 静态文件服务

// --- 认证中间件 ---
// 简单的认证，实际应用中应使用 JWT 或 Session
const AUTH_TOKEN = 'supersecrettoken'; // 硬编码的 token

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token == null) return res.status(401).json({ message: '未授权: 缺少 token' });

  if (token === AUTH_TOKEN) {
    next();
  } else {
    return res.status(403).json({ message: '未授权: token 无效' });
  }
}

// --- API 路由 ---

// 0. 登录接口
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;

  // 简单验证，实际应用中应查询数据库
  if (username === 'admin' && password === 'password') {
    res.json({ message: '登录成功', token: AUTH_TOKEN });
  } else {
    res.status(401).json({ message: '用户名或密码错误' });
  }
});

// 1. 视频上传接口 (无需认证)
app.post('/api/upload', upload.single('videoFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: '文件上传失败，没有找到文件' });
  }

  const { userId, title } = req.body; // 接收 title
  if (!userId) {
    return res.status(400).json({ message: '缺少 userId' });
  }

  const fileUrl = `http://localhost:${port}/uploads/${req.file.filename}`;

  try {
    const [newVideo] = await db('videos').insert({
      filename: req.file.filename,
      url: fileUrl,
      user_id: userId,
      title: title, // 保存 title
      status: 'pending',
    }).returning('*');

    console.log('视频信息已存入数据库:', newVideo);
    res.status(200).json({ message: '上传成功，等待审核', data: newVideo });
  } catch (error) {
    console.error('数据库插入失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 2. 获取待审核视频列表 (需要认证)
app.get('/api/admin/videos', authenticateToken, async (req, res) => {
  try {
    const pendingVideos = await db('videos').where('status', 'pending').orderBy('created_at', 'desc');
    res.status(200).json(pendingVideos);
  } catch (error) {
    console.error('查询待审核视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 3. 批准视频 (需要认证)
app.post('/api/videos/:id/approve', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const updated = await db('videos').where('id', id).update({ status: 'approved' });
    if (updated) {
      res.status(200).json({ message: '视频已批准' });
    } else {
      res.status(404).json({ message: '视频未找到' });
    }
  } catch (error) {
    console.error('批准视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 4. 拒绝视频 (需要认证)
app.post('/api/videos/:id/reject', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const updated = await db('videos').where('id', id).update({ status: 'rejected' });
    if (updated) {
      res.status(200).json({ message: '视频已拒绝' });
    } else {
      res.status(404).json({ message: '视频未找到' });
    }
  } catch (error) {
    console.error('拒绝视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 5. 获取指定用户上传的视频列表 (无需认证，小程序端使用)
app.get('/api/my/videos', async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.status(400).json({ message: '缺少 userId' });
    }
    const userVideos = await db('videos').where('user_id', userId).orderBy('created_at', 'desc');
    res.status(200).json(userVideos);
  } catch (error) {
    console.error('查询用户视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// --- 启动服务器 ---
async function startServer() {
  await setupDatabase();
  app.listen(port, () => {
    console.log(`后台服务已启动，正在监听 http://localhost:${port}`);
  });
}

startServer();
