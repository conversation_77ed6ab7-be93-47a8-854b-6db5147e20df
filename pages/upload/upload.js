// pages/upload/upload.js
Page({
  data: {
    tempFilePath: '', // 临时视频路径
    poster: '', // 视频封面
    title: '', // 视频标题
    isUploading: false, // 是否正在上传
    uploadProgress: 0, // 上传进度
  },

  /**
   * 监听标题输入
   */
  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    });
  },

  /**
   * 选择视频
   */
  chooseVideo() {
    if (this.data.isUploading) return;

    wx.chooseMedia({
      count: 1,
      mediaType: ['video'],
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        console.log("选择视频成功", res);
        const tempFile = res.tempFiles[0];
        this.setData({
          tempFilePath: tempFile.tempFilePath,
          poster: tempFile.thumbTempFilePath, // 获取视频封面
        });
      },
      fail: (err) => {
        if (err.errMsg !== "chooseMedia:fail cancel") {
          wx.showToast({ title: '选择视频失败', icon: 'none' });
        }
      }
    });
  },

  /**
   * 上传视频
   */
  uploadVideo() {
    if (!this.data.tempFilePath) {
      wx.showToast({ title: '请先选择视频', icon: 'none' });
      return;
    }
    if (!this.data.title.trim()) {
      wx.showToast({ title: '请填写视频标题', icon: 'none' });
      return;
    }

    this.setData({ isUploading: true, uploadProgress: 0 });

    const uploadTask = wx.uploadFile({
      url: 'http://localhost:3000/api/upload',
      filePath: this.data.tempFilePath,
      name: 'videoFile',
      formData: {
        userId: 'test-user-123', // 【重要】这里应该是动态获取的真实用户ID
        title: this.data.title
      },
      success: (res) => {
        if (res.statusCode === 200) {
          wx.showToast({ title: '上传成功，等待审核', icon: 'success' });
          this.resetForm();
          // 跳转到“我的上传”页面，查看状态
          wx.navigateTo({ url: '/pages/my-uploads/my-uploads' });
        } else {
          this.handleUploadError(res.data);
        }
      },
      fail: (err) => {
        this.handleUploadError(err);
      },
      complete: () => {
        this.setData({ isUploading: false });
      }
    });

    uploadTask.onProgressUpdate((res) => {
      this.setData({ uploadProgress: res.progress });
    });
  },

  /**
   * 处理上传错误
   */
  handleUploadError(err) {
    console.error('上传失败', err);
    let errorMsg = '上传失败，请稍后重试';
    if (typeof err === 'object' && err.message) {
      errorMsg = err.message;
    } else if (typeof err === 'string') {
      try {
        const parsed = JSON.parse(err);
        if(parsed && parsed.message) {
          errorMsg = parsed.message;
        }
      } catch(e) {}
    }
    wx.showToast({ title: errorMsg, icon: 'none' });
    this.setData({ uploadProgress: 0 });
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      tempFilePath: '',
      poster: '',
      title: '',
      uploadProgress: 0
    });
  }
});