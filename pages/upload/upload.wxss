/* pages/upload/upload.wxss */
page {
  background-color: #f0f2f5; /* 极简浅灰色背景 */
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.page-container {
  width: 90%;
  max-width: 700rpx;
  padding: 40rpx 30rpx;
  background-color: #ffffff; /* 纯白色背景 */
  border-radius: 12rpx; /* 适中圆角 */
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06); /* 柔和阴影 */
  position: relative;
  overflow: hidden;
  height: 650rpx;
}

/* 上传区域 */
.upload-wrapper {
  width: 100%;
  margin-bottom: 10rpx;
}

.video-selector {
  width: 100%;
  height: 360rpx; /* 调整高度 */
  border: 2rpx dashed #d1d8e0; /* 简洁虚线 */
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa; /* 浅背景 */
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.video-selector:active {
  background-color: #e9ecef;
  transform: scale(0.99);
  border-color: #007bff;
}

.video-selector .icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 15rpx;
  opacity: 0.6;
}

.video-selector .tip {
  font-size: 30rpx;
  color: #6c757d;
  font-weight: 500;
}

.video-preview-wrapper {
  position: relative;
  width: 100%;
  height: 360rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.video-preview-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.re-choose-btn {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.re-choose-btn:active {
  background-color: rgba(0, 0, 0, 0.7);
}

/* 表单区域 */
.form-wrapper {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 0 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.form-item {
  display: flex;
  align-items: center;
  height: 90rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item .label {
  width: 160rpx;
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

.form-item .input {
  flex: 1;
  font-size: 28rpx;
  color: #343a40;
  padding-left: 10rpx;
  background-color: transparent;
}

/* 操作区域 */
.action-wrapper {
  width: 100%;
  margin-top: 30rpx;
}

.progress-box {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  background-color: #e9ecef;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

.progress-box progress {
  flex: 1;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #dee2e6;
}

.progress-box progress .wx-progress-bar {
  background-color: #28a745; /* 绿色 */
  border-radius: 8rpx;
  transition: width 0.3s ease-out;
}

.progress-box .progress-text {
  font-size: 26rpx;
  color: #343a40;
  margin-left: 15rpx;
  width: 70rpx;
  text-align: right;
  font-weight: 500;
}

.upload-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 34rpx;
  font-weight: bold;
  background-color: #007bff; /* 蓝色主色调 */
  color: white;
  box-shadow: 0 6rpx 15rpx rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease-in-out;
  letter-spacing: 1rpx;
}

.upload-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 8rpx rgba(0, 123, 255, 0.1);
}

.upload-btn[disabled] {
  background-color: #adb5bd;
  color: rgba(255, 255, 255, 0.8);
  box-shadow: none;
  cursor: not-allowed;
  pointer-events: none;
}

.footer-tip {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 90%; /* 宽度调整 */
  text-align: center;
  font-size: 26rpx; /* 字体大小 */
  color: #6c757d; /* 柔和颜色 */
  font-weight: normal;
  letter-spacing: 0.5rpx;
  background-color: rgba(255, 255, 255, 0.7); /* 更透明的背景 */
  padding: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.03);
  opacity: 0.9;
  z-index: 100;
}
