// pages/my-uploads/my-uploads.js
Page({
  data: {
    videos: [],
    loading: true,
    statusMap: {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝'
    }
  },

  onLoad(options) {
    this.fetchMyUploads();
  },

  onPullDownRefresh() {
    this.fetchMyUploads(() => {
      wx.stopPullDownRefresh();
    });
  },

  fetchMyUploads(callback) {
    this.setData({ loading: true });
    wx.request({
      url: 'http://localhost:3000/api/my/videos',
      method: 'GET',
      data: {
        userId: 'test-user-123' // 【重要】这里应该是动态获取的真实用户ID
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const formattedVideos = res.data.map(video => {
            // 简单处理一下时间格式
            video.created_at_formatted = new Date(video.created_at).toLocaleString();
            return video;
          });
          this.setData({ videos: formattedVideos });
        } else {
          wx.showToast({ title: '加载失败', icon: 'none' });
        }
      },
      fail: (err) => {
        console.error('请求失败', err);
        wx.showToast({ title: '网络错误', icon: 'none' });
      },
      complete: () => {
        this.setData({ loading: false });
        if (typeof callback === 'function') {
          callback();
        }
      }
    });
  },

  goToUpload() {
    wx.switchTab({ // 或者使用 navigateTo，取决于你的 tab-bar 配置
      url: '/pages/upload/upload'
    });
  }
});
